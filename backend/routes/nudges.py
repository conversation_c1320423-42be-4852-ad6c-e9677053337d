
from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
from datetime import datetime
import uuid

from models.nudge import Nudge, NudgeCreate, NudgeUpdate, NudgeStatus, NudgeRequest, NudgeResponse
from firebase.init import get_firestore_client

router = APIRouter()

@router.post("/msme/{msme_id}/nudge", response_model=NudgeResponse, status_code=status.HTTP_201_CREATED)
async def create_nudge_for_msme(msme_id: str, nudge_data: NudgeRequest):
    """Create and send a new nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge with exact structure as specified
        nudge_record = {
            'id': nudge_id,
            'trigger_type': nudge_data.trigger_type,
            'message': nudge_data.message,
            'medium': nudge_data.medium,
            'sent_at': datetime.utcnow().isoformat(),
            'status': 'sent'
        }
        
        # Save nudge to Firestore at specified path
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge_record)
        
        # Return success response with nudge details
        return NudgeResponse(
            nudge_id=nudge_id,
            msme_id=msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.utcnow(),
            status='sent'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create nudge: {str(e)}"
        )

@router.get("/msme/{msme_id}/nudges", response_model=List[Nudge])
async def get_nudge_history(msme_id: str, limit: int = 50):
    """Get nudge history for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query for nudge history
        query = db.collection('msmes').document(msme_id).collection('nudges')
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        nudges = []
        for doc in docs:
            data = doc.to_dict()
            # Convert to Nudge model format
            nudge = Nudge(
                nudge_id=data.get('id', doc.id),
                msme_id=msme_id,
                trigger_type=data.get('trigger_type'),
                message=data.get('message'),
                medium=data.get('medium'),
                sent_at=datetime.fromisoformat(data.get('sent_at').replace('Z', '+00:00')) if data.get('sent_at') else None,
                status=data.get('status', 'sent'),
                metadata=data.get('metadata', {})
            )
            nudges.append(nudge)
        
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudge history: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )
