
from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime
import uuid
import logging

from models.nudge import Nudge, NudgeUpdate, NudgeStatus, NudgeRequest, NudgeResponse, Medium
from firebase.init import get_firestore_client

router = APIRouter()

@router.post("/send", response_model=NudgeResponse, status_code=status.HTTP_201_CREATED)
async def send_nudge(nudge_data: NudgeRequest, background_tasks: BackgroundTasks):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(nudge_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge
        nudge = Nudge(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.utcnow(),
            status=NudgeStatus.PENDING,  # Initially set as pending
            metadata=nudge_data.metadata
        )
        
        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(nudge_data.msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge.dict())
        
        # Add delivery task to background tasks
        background_tasks.add_task(deliver_nudge, nudge)
        
        # Update status to SENT
        nudge.status = NudgeStatus.SENT
        nudge_ref.update({"status": NudgeStatus.SENT})
        
        # Return response
        return NudgeResponse(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=str(nudge_data.trigger_type),
            message=nudge_data.message,
            medium=str(nudge_data.medium),
            sent_at=datetime.utcnow(),
            status=str(NudgeStatus.SENT)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/msme/{msme_id}/nudges", response_model=List[Nudge])
async def get_nudge_history(msme_id: str, limit: int = 50):
    """Get nudge history for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query for nudge history
        query = db.collection('msmes').document(msme_id).collection('nudges')
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        nudges = []
        for doc in docs:
            data = doc.to_dict()
            # Convert to Nudge model format
            nudge = Nudge(
                nudge_id=data.get('id', doc.id),
                msme_id=msme_id,
                trigger_type=data.get('trigger_type'),
                message=data.get('message'),
                medium=data.get('medium'),
                sent_at=datetime.fromisoformat(data.get('sent_at').replace('Z', '+00:00')) if data.get('sent_at') else None,
                status=data.get('status', 'sent'),
                metadata=data.get('metadata', {})
            )
            nudges.append(nudge)
        
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudge history: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )
