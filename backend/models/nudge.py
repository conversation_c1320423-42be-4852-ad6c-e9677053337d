from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class TriggerType(str, Enum):
    SCORE_DROP = "score_drop"
    MANUAL = "manual"  # Added for manual nudges
    SCORE_IMPROVEMENT = "score_improvement"
    NEW_SIGNAL = "new_signal"
    PERIODIC_UPDATE = "periodic_update"
    RISK_BAND_CHANGE = "risk_band_change"

class Medium(str, Enum):
    WHATSAPP = "whatsapp"
    EMAIL = "email"
    SMS = "sms"
    PUSH_NOTIFICATION = "push_notification"

class NudgeStatus(str, Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    DELIVERED = "delivered"

class Nudge(BaseModel):
    nudge_id: Optional[str] = None
    msme_id: str = Field(..., min_length=1)
    trigger_type: TriggerType
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Medium
    sent_at: Optional[datetime] = None
    status: NudgeStatus = NudgeStatus.PENDING
    metadata: Optional[dict] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NudgeRequest(BaseModel):
    """Input validation for creating nudges"""
    trigger_type: TriggerType = Field(..., description="Must be either 'score_drop' or 'manual'")
    message: str = Field(..., min_length=1, max_length=500, description="Nudge message content")
    medium: Medium = Field(..., description="Must be either 'whatsapp' or 'email'")
    metadata: Optional[dict] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True

# Add NudgeCreate as an alias for NudgeRequest for backward compatibility
NudgeCreate = NudgeRequest

class NudgeResponse(BaseModel):
    """Response model for nudge operations"""
    nudge_id: str
    msme_id: str
    trigger_type: str
    message: str
    medium: str
    sent_at: datetime
    status: str
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NudgeHistory(BaseModel):
    """Model for retrieving nudge history"""
    nudges: List[Nudge]
    total_count: int
    last_updated: Optional[datetime] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
