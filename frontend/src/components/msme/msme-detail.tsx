
'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { MSME, ScoreDetails, Signal, Nudge, NudgeRequest } from '@/types';
import { SignalTimeline } from './signal-timeline';
import { ArrowLeft, Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle, BarChart3, Send, History, FileText, Phone, Mail, Globe, User, CreditCard, Target, Zap, MessageSquare } from 'lucide-react';

interface MSMEDetailProps {
  msmeId: string;
}

export function MSMEDetail({ msmeId }: MSMEDetailProps) {
  const [msme, setMsme] = useState<MSME | null>(null);
  const [scoreDetails, setScoreDetails] = useState<ScoreDetails | null>(null);
  const [signals, setSignals] = useState<Signal[]>([]);
  const [nudges, setNudges] = useState<Nudge[]>([]);
  const [loading, setLoading] = useState(true);
  const [signalsLoading, setSignalsLoading] = useState(false);
  const [nudgesLoading, setNudgesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Nudge form state
  const [isNudgeDialogOpen, setIsNudgeDialogOpen] = useState(false);
  const [nudgeMessage, setNudgeMessage] = useState('');
  const [nudgeMedium, setNudgeMedium] = useState<'whatsapp' | 'email'>('email');
  const [sendingNudge, setSendingNudge] = useState(false);
  
  const { toast } = useToast();

  useEffect(() => {
    async function fetchMSMEData() {
      try {
        setLoading(true);
        const [msmeData, scoreData] = await Promise.all([
          api.getMSME(msmeId),
          api.getMSMEScore(msmeId)
        ]);
        setMsme(msmeData);
        setScoreDetails(scoreData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch MSME data');
      } finally {
        setLoading(false);
      }
    }

    fetchMSMEData();
  }, [msmeId]);

  // Fetch signals when signals tab is accessed
  const fetchSignals = async () => {
    if (signals.length > 0) return; // Already loaded

    try {
      setSignalsLoading(true);
      const signalsData = await api.getMSMESignals(msmeId, 20);
      setSignals(signalsData);
    } catch (err) {
      console.error('Failed to fetch signals:', err);
    } finally {
      setSignalsLoading(false);
    }
  };

  // Fetch nudges when nudges tab is accessed
  const fetchNudges = async () => {
    try {
      setNudgesLoading(true);
      const nudgesData = await api.getNudgeHistory(msmeId, 50);
      setNudges(nudgesData);
    } catch (err) {
      console.error('Failed to fetch nudges:', err);
      toast({
        title: "Error",
        description: "Failed to fetch nudge history",
        variant: "destructive",
      });
    } finally {
      setNudgesLoading(false);
    }
  };

  // Send nudge
  const handleSendNudge = async () => {
    if (!nudgeMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter a message",
        variant: "destructive",
      });
      return;
    }

    try {
      setSendingNudge(true);
      const nudgeRequest: NudgeRequest = {
        trigger_type: 'manual',
        message: nudgeMessage.trim(),
        medium: nudgeMedium,
        metadata: {
          sent_by: 'admin',
          timestamp: new Date().toISOString()
        }
      };

      await api.sendNudge(msmeId, nudgeRequest);
      
      toast({
        title: "Success",
        description: "Nudge sent successfully",
      });

      // Reset form and close dialog
      setNudgeMessage('');
      setNudgeMedium('email');
      setIsNudgeDialogOpen(false);
      
      // Refresh nudge history
      fetchNudges();
      
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to send nudge",
        variant: "destructive",
      });
    } finally {
      setSendingNudge(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get medium badge variant
  const getMediumBadgeVariant = (medium: string) => {
    switch (medium) {
      case 'whatsapp':
        return 'default';
      case 'email':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Get medium icon
  const getMediumIcon = (medium: string) => {
    switch (medium) {
      case 'whatsapp':
        return <MessageSquare className="h-3 w-3" />;
      case 'email':
        return <Mail className="h-3 w-3" />;
      default:
        return <Send className="h-3 w-3" />;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid gap-6 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !msme || !scoreDetails) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Failed to load MSME data'}</p>
            <Button asChild className="mt-4">
              <Link href="/">Return to Dashboard</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  function getRiskBadgeVariant(risk_band: string): "destructive" | "default" | "secondary" | "outline" | null | undefined {
    throw new Error('Function not implemented.');
  }

  function getRiskLabel(risk_band: string): import("react").ReactNode {
    throw new Error('Function not implemented.');
  }

  function getTrendIcon(score_trend: string | undefined): import("react").ReactNode {
    throw new Error('Function not implemented.');
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/portfolio">Portfolio</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbPage>{msme.name}</BreadcrumbPage>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Header with Send Nudge Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/portfolio">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Portfolio
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{msme.name}</h1>
            <p className="text-muted-foreground">{msme.business_type} • {msme.location}</p>
          </div>
        </div>
        
        {/* Send Nudge Button */}
        <Dialog open={isNudgeDialogOpen} onOpenChange={setIsNudgeDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Send className="h-4 w-4" />
              Send Nudge
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Send Nudge to {msme.name}</DialogTitle>
              <DialogDescription>
                Send a manual notification to this MSME via WhatsApp or Email.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  placeholder="Enter your message here..."
                  value={nudgeMessage}
                  onChange={(e) => setNudgeMessage(e.target.value)}
                  maxLength={500}
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  {nudgeMessage.length}/500 characters
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="medium">Medium</Label>
                <Select value={nudgeMedium} onValueChange={(value: 'whatsapp' | 'email') => setNudgeMedium(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select medium" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email
                      </div>
                    </SelectItem>
                    <SelectItem value="whatsapp">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        WhatsApp
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsNudgeDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSendNudge} disabled={sendingNudge || !nudgeMessage.trim()}>
                {sendingNudge ? 'Sending...' : 'Send Nudge'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Score Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-primary/5 to-primary/10">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-primary">
              <div className="p-2 bg-primary/10 rounded-lg">
                <BarChart3 className="h-5 w-5" />
              </div>
              Credit Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-5xl font-bold text-primary mb-3">
              {scoreDetails.current_score}
            </div>
            <div className="space-y-2">
              <Badge variant={getRiskBadgeVariant(scoreDetails.risk_band)} className="font-medium">
                {getRiskLabel(scoreDetails.risk_band)} Risk
              </Badge>
              <div className="flex items-center gap-2">
                {getTrendIcon(msme.score_trend)}
                <span className="text-sm text-muted-foreground capitalize">
                  {msme.score_trend || 'stable'} trend
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Target className="h-5 w-5 text-blue-500" />
              </div>
              Signals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">
              {scoreDetails.signals_count}
            </div>
            <p className="text-sm text-muted-foreground">
              Data points collected
            </p>
            <Progress value={(scoreDetails.signals_count / 20) * 100} className="mt-3 h-2" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Calendar className="h-5 w-5 text-green-500" />
              </div>
              Last Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium mb-2">
              {new Date(msme.last_signal_date).toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              {Math.floor((Date.now() - new Date(msme.last_signal_date).getTime()) / (1000 * 60 * 60 * 24))} days ago
            </p>
            {msme.recent_nudges > 0 && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {msme.recent_nudges} alerts
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Zap className="h-5 w-5 text-purple-500" />
              </div>
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="sm" variant="outline" className="w-full" disabled>
                  <Send className="h-4 w-4 mr-2" />
                  Send Nudge
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Send risk alert notification</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="sm" variant="outline" className="w-full" disabled>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download detailed report</p>
              </TooltipContent>
            </Tooltip>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Card className="border-0 shadow-lg">
        <Tabs defaultValue="score" className="w-full" onValueChange={(value) => {
          if (value === 'signals') {
            fetchSignals();
          } else if (value === 'nudges') {
            fetchNudges();
          }
        }}>
          <CardHeader className="pb-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="score">Score Analysis</TabsTrigger>
              <TabsTrigger value="profile">Business Profile</TabsTrigger>
              <TabsTrigger value="signals">Signal History</TabsTrigger>
              <TabsTrigger value="nudges">Nudge History</TabsTrigger>
              <TabsTrigger value="actions">Action Center</TabsTrigger>
            </TabsList>
          </CardHeader>

          <CardContent>
            <TabsContent value="score" className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Credit Score Breakdown</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-primary">Score Components</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <span className="font-medium">Base Score:</span>
                        <span className="font-mono text-lg font-bold text-green-600">
                          {scoreDetails.score_breakdown.base_score}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                        <span className="font-medium">GST Penalty:</span>
                        <span className="font-mono text-lg font-bold text-red-600">
                          -{scoreDetails.score_breakdown.gst_penalty}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                        <span className="font-medium">UPI Penalty:</span>
                        <span className="font-mono text-lg font-bold text-red-600">
                          -{scoreDetails.score_breakdown.upi_penalty}
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                        <span className="font-medium">Reviews Penalty:</span>
                        <span className="font-mono text-lg font-bold text-red-600">
                          -{scoreDetails.score_breakdown.reviews_penalty}
                        </span>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center p-4 bg-primary/10 rounded-lg border border-primary/20">
                        <span className="font-bold text-lg">Final Score:</span>
                        <span className="font-mono text-2xl font-bold text-primary">
                          {scoreDetails.current_score}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold text-primary">Analysis Details</h4>
                    <div className="space-y-3">
                      {Object.entries(scoreDetails.score_breakdown.details).map(([key, value]) => (
                        <div key={key} className="p-3 bg-muted/50 rounded-lg border">
                          <div className="font-medium capitalize text-sm text-muted-foreground mb-1">
                            {key.replace(/_/g, ' ')}
                          </div>
                          <div className="text-sm">{value}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="profile" className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Business Information</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        Basic Details
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Business Name:</span>
                          <span className="font-medium">{msme.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Type:</span>
                          <Badge variant="outline" className="capitalize">{msme.business_type}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Location:</span>
                          <span className="font-medium">{msme.location}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Registered:</span>
                          <span className="font-medium">
                            {new Date(msme.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Contact Information
                      </h4>
                      <div className="space-y-3">
                        <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                          <Phone className="h-4 w-4 mr-2" />
                          Contact Number
                        </Button>
                        <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                          <Mail className="h-4 w-4 mr-2" />
                          Email Address
                        </Button>
                        <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                          <Globe className="h-4 w-4 mr-2" />
                          Website
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="signals" className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Signal History</h3>
                  <Badge variant="secondary">{scoreDetails.signals_count} total</Badge>
                </div>

                <SignalTimeline
                  signals={signals}
                  loading={signalsLoading}
                />
              </div>
            </TabsContent>

            <TabsContent value="nudges" className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Nudge History</h3>
                  <p className="text-sm text-muted-foreground">
                    All notifications sent to this MSME
                  </p>
                </div>
                <Button variant="outline" onClick={fetchNudges} disabled={nudgesLoading}>
                  <History className="h-4 w-4 mr-2" />
                  {nudgesLoading ? 'Loading...' : 'Refresh'}
                </Button>
              </div>

              {nudgesLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-20 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : nudges.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No nudges sent yet</h3>
                    <p className="text-muted-foreground text-center mb-4">
                      Send your first nudge to start engaging with this MSME
                    </p>
                    <Button onClick={() => setIsNudgeDialogOpen(true)}>
                      <Send className="h-4 w-4 mr-2" />
                      Send First Nudge
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {nudges.map((nudge) => (
                    <Card key={nudge.nudge_id} className="border-l-4 border-l-primary">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={getMediumBadgeVariant(nudge.medium)} className="gap-1">
                                {getMediumIcon(nudge.medium)}
                                {nudge.medium.charAt(0).toUpperCase() + nudge.medium.slice(1)}
                              </Badge>
                              <Badge variant="outline">
                                {nudge.trigger_type === 'manual' ? 'Manual' : 'Score Drop'}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {formatDate(nudge.sent_at)}
                              </span>
                            </div>
                            <p className="text-sm leading-relaxed">{nudge.message}</p>
                          </div>
                          <Badge variant={nudge.status === 'sent' ? 'default' : 'secondary'}>
                            {nudge.status.charAt(0).toUpperCase() + nudge.status.slice(1)}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="actions" className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Action Center</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold">Risk Management</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <Send className="mr-2 h-4 w-4" />
                        Send Risk Alert
                      </Button>
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <AlertTriangle className="mr-2 h-4 w-4" />
                        Create Nudge
                      </Button>
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <History className="mr-2 h-4 w-4" />
                        View Alert History
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold">Reports & Analysis</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <FileText className="mr-2 h-4 w-4" />
                        Generate Credit Report
                      </Button>
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Score Trend Analysis
                      </Button>
                      <Button variant="outline" className="w-full justify-start" disabled>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Export Data
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </CardContent>
        </Tabs>
      </Card>

      {/* Footer */}
      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
        <Calendar className="h-4 w-4" />
        <span>Last updated: {new Date(scoreDetails.last_updated).toLocaleString()}</span>
      </div>
    </div>
  );
}
