
export interface MSME {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'manufacturing' | 'services';
  location: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_trend?: 'improving' | 'declining' | 'stable';
  signals_count: number;
  recent_nudges: number;
  last_signal_date: string;
  created_at: string;
  tags: string[];
}

export interface Analytics {
  total_msmes: number;
  total_signals: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: {
    [key: string]: number;
  };
  average_signals_per_msme: number;
  last_updated: string;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: {
    base_score: number;
    gst_penalty: number;
    reviews_penalty: number;
    upi_penalty: number;
    details: {
      [key: string]: string;
    };
  };
  signals_count: number;
  last_updated: string;
}

export type SignalSource = 'gst' | 'upi' | 'reviews' | 'justdial' | 'instagram' | 'maps';

export interface Signal {
  signal_id: string;
  msme_id: string;
  source: SignalSource;
  value: any; // Raw signal value (could be number, string, dict)
  normalized: number; // Normalized score 0-1
  timestamp: string;
  metadata: Record<string, any>;
}

export interface SignalInput {
  source: SignalSource;
  value: any;
  metadata?: Record<string, any>;
  timestamp?: string;
}

export interface Nudge {
  nudge_id: string;
  msme_id: string;
  trigger_type: 'score_drop' | 'manual' | 'score_improvement' | 'new_signal' | 'periodic_update' | 'risk_band_change';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms' | 'push_notification';
  sent_at: string;
  status: 'pending' | 'sent' | 'failed' | 'delivered';
  metadata?: Record<string, any>;
}

export interface NudgeRequest {
  trigger_type: 'score_drop' | 'manual';
  message: string;
  medium: 'whatsapp' | 'email';
  metadata?: Record<string, any>;
}

export interface NudgeResponse {
  nudge_id: string;
  msme_id: string;
  trigger_type: string;
  message: string;
  medium: string;
  sent_at: string;
  status: string;
}
